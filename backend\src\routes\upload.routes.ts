import express from 'express';
import { handleImageUpload } from '../controllers/upload.controller.js';
import { authenticateJWT } from '../middlewares/auth.middleware.js';

const router = express.Router();

// Route pour l'upload d'images (temporairement sans auth pour test)
router.post('/image', handleImageUpload);

// Route de test sans authentification
router.post('/test', handleImageUpload);

export default router;
