import express from 'express';
import { handleImageUpload, upload } from '../controllers/upload.controller.js';
import { authenticateJWT } from '../middlewares/auth.middleware.js';

const router = express.Router();

// Middleware pour gérer les erreurs multer
const handleMulterError = (err: any, req: any, res: any, next: any) => {
    if (err) {
        console.error('Erreur multer:', err);
        return res.status(400).json({ message: err.message || 'Erreur lors de l\'upload' });
    }
    next();
};

// Route pour l'upload d'images avec multer middleware (sans auth pour test)
router.post('/image', upload.single('image'), handleMulterError, handleImageUpload);

// Route de test sans authentification
router.post('/test', upload.single('image'), handleMulterError, handleImageUpload);

export default router;
