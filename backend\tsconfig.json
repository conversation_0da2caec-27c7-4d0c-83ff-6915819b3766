{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "moduleResolution": "NodeNext", "outDir": "./dist", "rootDir": "./src", "strict": false, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "allowJs": true, "checkJs": false, "noImplicitAny": false, "noEmitOnError": true, "skipDefaultLibCheck": true, "ignoreDeprecations": "5.0", "noFallthroughCasesInSwitch": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false}, "ts-node": {"transpileOnly": true, "esm": true, "experimentalSpecifierResolution": "node"}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}