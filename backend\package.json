{"name": "backend", "version": "1.0.0", "main": "dist/index.js", "type": "module", "scripts": {"start": "node dist/index.js", "build": "tsc --build", "dev": "nodemon -e ts --exec \"node --loader ts-node/esm src/server.ts\"", "clean": "if exist dist rmdir /s /q dist", "prebuild": "npm run clean", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@types/leaflet": "^1.9.18", "bcrypt": "^5.1.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.2", "express-validator": "^7.0.1", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "pg": "^8.11.3"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/cors": "^2.8.17", "@types/express": "^5.0.1", "@types/jsonwebtoken": "^9.0.6", "@types/multer": "^1.4.13", "@types/node": "^22.13.10", "@types/pg": "^8.10.9", "nodemon": "^3.1.9", "ts-node": "^10.9.2", "ts-node-dev": "^2.0.0", "typescript": "^5.8.2"}}