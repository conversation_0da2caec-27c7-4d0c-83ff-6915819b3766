import { Request, Response } from 'express';

// Contrôleur simple pour l'upload d'image (temporaire - sans multer)
export const handleImageUpload = async (req: Request, res: Response) => {
    try {
        // Pour l'instant, on simule un upload réussi
        // En production, vous devriez utiliser un service comme AWS S3, Cloudinary, etc.
        const mockImageUrl = `https://via.placeholder.com/400x300?text=Image+${Date.now()}`;

        res.status(200).json({
            message: 'Image uploadée avec succès (simulé)',
            imageUrl: mockImageUrl,
            filename: `mock-image-${Date.now()}.jpg`
        });
    } catch (error) {
        console.error('Erreur lors de l\'upload:', error);
        res.status(500).json({ message: 'Erreur lors de l\'upload de l\'image' });
    }
};

export default {
    handleImageUpload
};
