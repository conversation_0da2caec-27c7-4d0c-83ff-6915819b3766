// Utility functions for handling image URLs

/**
 * Converts a relative image URL to a full URL that works in the current environment
 * @param imageUrl - The relative image URL from the backend (e.g., "/uploads/images/filename.jpg")
 * @returns The full URL that can be used in the frontend
 */
export const getImageUrl = (imageUrl: string | null | undefined): string | null => {
    if (!imageUrl) return null;
    
    // If it's already a full URL, return as is
    if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
        return imageUrl;
    }
    
    // For development, use the backend URL directly
    // In production, this would be configured differently
    const backendUrl = 'http://localhost:3000';
    
    // Remove leading slash if present and add backend URL
    const cleanPath = imageUrl.startsWith('/') ? imageUrl.substring(1) : imageUrl;
    return `${backendUrl}/${cleanPath}`;
};

/**
 * Checks if an image URL is valid and accessible
 * @param imageUrl - The image URL to check
 * @returns Promise that resolves to true if image is accessible
 */
export const isImageAccessible = async (imageUrl: string): Promise<boolean> => {
    try {
        const response = await fetch(imageUrl, { method: 'HEAD' });
        return response.ok;
    } catch {
        return false;
    }
};
