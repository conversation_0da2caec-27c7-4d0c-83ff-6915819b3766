<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="dataSourceStorageLocal" created-in="WS-243.26053.12">
    <data-source name="postgres@localhost" uuid="8dc5ec82-d1bb-44f2-8c95-4748a7a110e7">
      <database-info product="PostgreSQL" version="15.4 (Debian 15.4-1.pgdg110+1)" jdbc-version="4.2" driver-name="PostgreSQL JDBC Driver" driver-version="42.7.3" dbms="POSTGRES" exact-version="15.4" exact-driver-version="42.7">
        <identifier-quote-string>&quot;</identifier-quote-string>
      </database-info>
      <case-sensitivity plain-identifiers="lower" quoted-identifiers="exact" />
      <secret-storage>master_key</secret-storage>
      <user-name>user</user-name>
      <schema-mapping>
        <introspection-scope>
          <node negative="1">
            <node kind="database" qname="@">
              <node kind="schema" qname="@" />
            </node>
            <node kind="database" qname="nextdoorbuddy">
              <node kind="schema" negative="1" />
            </node>
          </node>
        </introspection-scope>
      </schema-mapping>
    </data-source>
  </component>
</project>