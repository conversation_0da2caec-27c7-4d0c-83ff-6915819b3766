import express from 'express';
import {
    createTroc,
    getTrocByUserQuartier,
    getUserTrocs,
    updateTroc,
    deleteTroc,
    adminGetAllTrocs,
    adminUpdateTrocStatus,
    adminGetTrocStats
} from '../controllers/troc.controller.js';
import { authenticateJWT, isAdmin } from '../middlewares/auth.middleware.js';

const router = express.Router();

// Route de test simple
router.get('/test', (req, res) => {
    res.json({ message: 'Troc routes working!' });
});

// Routes pour les annonces de troc (temporairement sans auth pour debug)
router.post('/', createTroc);
router.get('/', getTrocByUserQuartier);
router.get('/my-trocs', getUserTrocs);
router.put('/:id', updateTroc);
router.delete('/:id', deleteTroc);

// Routes admin
router.get('/admin/all', authenticateJWT, isAdmin, adminGetAllTrocs);
router.patch('/admin/:id/status', authenticateJWT, isAdmin, adminUpdateTrocStatus);
router.get('/admin/stats', authenticateJWT, isAdmin, adminGetTrocStats);

export default router;
