<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="549b3728-53cb-4adf-9f07-a61427b4c3b5" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/frontend/nextdoorbuddy/src/pages/mapUtil/MapWithDraw.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/dataSources/8dc5ec82-d1bb-44f2-8c95-4748a7a110e7.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/dataSources/8dc5ec82-d1bb-44f2-8c95-4748a7a110e7.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/dataSources/8dc5ec82-d1bb-44f2-8c95-4748a7a110e7/storage_v2/_src_/database/nextdoorbuddy.RcIzrw/schema/information_schema.FNRwLQ.meta" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/dataSources/8dc5ec82-d1bb-44f2-8c95-4748a7a110e7/storage_v2/_src_/database/nextdoorbuddy.RcIzrw/schema/information_schema.FNRwLQ.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/dataSources/8dc5ec82-d1bb-44f2-8c95-4748a7a110e7/storage_v2/_src_/database/nextdoorbuddy.RcIzrw/schema/pg_catalog.0S1ZNQ.meta" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/dataSources/8dc5ec82-d1bb-44f2-8c95-4748a7a110e7/storage_v2/_src_/database/nextdoorbuddy.RcIzrw/schema/pg_catalog.0S1ZNQ.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/dataSources/8dc5ec82-d1bb-44f2-8c95-4748a7a110e7/storage_v2/_src_/database/nextdoorbuddy.RcIzrw/schema/public.abK9xQ.meta" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/dataSources/8dc5ec82-d1bb-44f2-8c95-4748a7a110e7/storage_v2/_src_/database/nextdoorbuddy.RcIzrw/schema/public.abK9xQ.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/dataSources/8dc5ec82-d1bb-44f2-8c95-4748a7a110e7/storage_v2/_src_/database/postgres.edMnLQ/schema/public.abK9xQ.meta" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/dataSources/8dc5ec82-d1bb-44f2-8c95-4748a7a110e7/storage_v2/_src_/database/postgres.edMnLQ/schema/public.abK9xQ.meta" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/.idea/workspace.xml" beforeDir="false" afterPath="$PROJECT_DIR$/.idea/workspace.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/controllers/quartier.controller.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/controllers/quartier.controller.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/backend/src/models/quartier.model.ts" beforeDir="false" afterPath="$PROJECT_DIR$/backend/src/models/quartier.model.ts" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/docker/init/01_schema.sql" beforeDir="false" afterPath="$PROJECT_DIR$/docker/init/01_schema.sql" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/nextdoorbuddy/src/pages/AdminQuartiers.tsx" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/nextdoorbuddy/src/pages/AdminQuartiers.tsx" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/frontend/nextdoorbuddy/vite.config.ts" beforeDir="false" afterPath="$PROJECT_DIR$/frontend/nextdoorbuddy/vite.config.ts" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="TypeScript File" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="GitHubPullRequestSearchHistory">{
  &quot;lastFilter&quot;: {
    &quot;state&quot;: &quot;OPEN&quot;,
    &quot;assignee&quot;: &quot;Pierre63628&quot;
  }
}</component>
  <component name="GithubPullRequestsUISettings">{
  &quot;selectedUrlAndAccountId&quot;: {
    &quot;url&quot;: &quot;https://github.com/Pierre63628/ProjetAnnuel3A.git&quot;,
    &quot;accountId&quot;: &quot;********-e9e2-41b7-a7ff-292131952ba1&quot;
  }
}</component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 0
}</component>
  <component name="ProjectId" id="2xNSeiSOhXkAhVsNQwJMQ5u9Zi8" />
  <component name="ProjectLevelVcsManager">
    <OptionsSetting value="false" id="Update" />
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;Docker.docker-compose.yaml.db: Compose Deployment.executor&quot;: &quot;Run&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;database.data.extractors.current.export.id&quot;: &quot;SQL Inserts&quot;,
    &quot;git-widget-placeholder&quot;: &quot;main&quot;,
    &quot;last_opened_file_path&quot;: &quot;/Users/<USER>/WebstormProjects/ProjetAnnuel3A/backend&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.Front.executor&quot;: &quot;Run&quot;,
    &quot;npm.build.executor&quot;: &quot;Run&quot;,
    &quot;npm.dev (1).executor&quot;: &quot;Run&quot;,
    &quot;npm.dev.executor&quot;: &quot;Run&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;ts.external.directory.path&quot;: &quot;/Users/<USER>/WebstormProjects/ProjetAnnuel3A/frontend/nextdoorbuddy/node_modules/typescript/lib&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;postgresql&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/backend" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="$PROJECT_DIR$/backend/sqldata" />
    </key>
  </component>
  <component name="RunManager" selected="npm.dev">
    <configuration default="true" type="docker-deploy" factoryName="docker-compose.yml" temporary="true">
      <deployment type="docker-compose.yml">
        <settings />
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="docker-compose.yaml.db: Compose Deployment" type="docker-deploy" factoryName="docker-compose.yml" temporary="true" server-name="Docker">
      <deployment type="docker-compose.yml">
        <settings>
          <option name="services">
            <list>
              <option value="db" />
            </list>
          </option>
          <option name="sourceFilePath" value="docker-compose.yaml" />
        </settings>
      </deployment>
      <method v="2" />
    </configuration>
    <configuration name="build" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/backend/package.json" />
      <command value="run" />
      <scripts>
        <script value="build" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="dev (1)" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/backend/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/frontend/nextdoorbuddy/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="Docker.docker-compose.yaml.db: Compose Deployment" />
      <item itemvalue="npm.build" />
      <item itemvalue="npm.dev (1)" />
      <item itemvalue="npm.dev" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="npm.dev" />
        <item itemvalue="npm.build" />
        <item itemvalue="npm.dev (1)" />
        <item itemvalue="Docker.docker-compose.yaml.db: Compose Deployment" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-WS-243.26053.12" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="549b3728-53cb-4adf-9f07-a61427b4c3b5" name="Changes" comment="" />
      <created>1747777135552</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747777135552</updated>
      <workItem from="1747777137215" duration="102000" />
      <workItem from="1747777248472" duration="64000" />
      <workItem from="1747777322737" duration="9833000" />
      <workItem from="1749493840445" duration="3556000" />
      <workItem from="1749504226126" duration="1637000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
</project>